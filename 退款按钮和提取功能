// ==UserScript==
// @name         微信小商店多功能助手
// @namespace    http://tampermonkey.net/
// @version      4.1
// @description  在订单列表页提供自动点击功能，在售后详情页通过后台预加载实现信息极速提取。
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/order/list*
// @match        https://store.weixin.qq.com/shop/aftersale/detail?orderid=*
// @grant        GM_addStyle
// @license      MIT
// ==/UserScript==

(function() {
    'use strict';

    // --- 变量定义 ---
    let extractedInfo = null; // 用于缓存预提取的数据
    let extractionInProgress = true; // 标记后台提取是否仍在进行

    // --- 样式注入 ---
    GM_addStyle(`
        #info-panel {
            position: fixed;
            top: 150px;
            right: 20px;
            width: 300px;
            background-color: white;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            color: #333;
        }
        #info-panel-header {
            padding: 10px;
            cursor: move;
            background-color: #f7f7f7;
            border-bottom: 1px solid #dcdcdc;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
        }
        #info-panel-title {
            font-weight: bold;
        }
        #info-panel-toggle {
            cursor: pointer;
            padding: 2px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 12px;
        }
        #info-panel-content {
            padding: 10px;
            display: block;
        }
        #info-panel.collapsed #info-panel-content {
            display: none;
        }
        .panel-button {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            background-color: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .panel-button:hover {
            background-color: #06ad56;
        }
        #info-display {
            width: 100%;
            height: 200px;
            box-sizing: border-box;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 12px;
            line-height: 1.6;
            resize: vertical;
        }
    `);

    // --- UI创建与交互 ---
    const panel = document.createElement('div');
    panel.id = 'info-panel';
    panel.innerHTML = `
        <div id="info-panel-header">
            <span id="info-panel-title"></span>
            <span id="info-panel-toggle">收起</span>
        </div>
        <div id="info-panel-content"></div>
    `;
    document.body.appendChild(panel);

    const infoPanel = document.querySelector('#info-panel');
    const panelHeader = document.querySelector('#info-panel-header');
    const panelTitle = document.querySelector('#info-panel-title');
    const panelContent = document.querySelector('#info-panel-content');
    const toggleButton = document.querySelector('#info-panel-toggle');

    toggleButton.addEventListener('click', () => {
        infoPanel.classList.toggle('collapsed');
        toggleButton.textContent = infoPanel.classList.contains('collapsed') ? '展开' : '收起';
    });

    let isDragging = false;
    let offsetX, offsetY;
    panelHeader.addEventListener('mousedown', (e) => {
        isDragging = true;
        offsetX = e.clientX - infoPanel.offsetLeft;
        offsetY = e.clientY - infoPanel.offsetTop;
        panelHeader.style.cursor = 'grabbing';
    });
    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        infoPanel.style.left = `${e.clientX - offsetX}px`;
        infoPanel.style.top = `${e.clientY - offsetY}px`;
    });
    document.addEventListener('mouseup', () => {
        isDragging = false;
        panelHeader.style.cursor = 'move';
    });

    // --- 页面逻辑分发 ---
    function initializePanel() {
        if (window.location.href.includes('/shop/order/list')) {
            setupListPage();
        } else if (window.location.href.includes('/shop/aftersale/detail')) {
            setupDetailPage();
        }
    }

    // --- 订单列表页功能 ---
    function setupListPage() {
        panelTitle.textContent = "订单列表助手";
        panelContent.innerHTML = `
            <button id="auto-click-button" class="panel-button">自动点击退款订单</button>
            <div id="list-page-feedback" style="font-size: 12px; color: #888;"></div>
        `;

        const autoClickButton = document.querySelector('#auto-click-button');
        const feedbackDiv = document.querySelector('#list-page-feedback');

        autoClickButton.addEventListener('click', () => {
            feedbackDiv.textContent = "正在查找链接...";
            const microAppHost = document.querySelector('micro-app[name="order"]');
            const shadowRoot = microAppHost ? microAppHost.shadowRoot : null;

            if (!shadowRoot) {
                feedbackDiv.textContent = "错误：未能找到微应用容器。";
                return;
            }

            const links = shadowRoot.querySelectorAll('.link');
            let found = false;
            for (const link of links) {
                if (link.innerText.trim() === '已全部退款') {
                    link.click();
                    feedbackDiv.textContent = "已点击第一个“已全部退款”链接。";
                    found = true;
                    break;
                }
            }

            if (!found) {
                feedbackDiv.textContent = "未找到“已全部退款”链接。";
            }
        });
    }

    // --- 售后详情页功能 ---
    function setupDetailPage() {
        panelTitle.textContent = "信息提取面板";
        panelContent.innerHTML = `
            <button id="extract-button" class="panel-button">提取信息</button>
            <textarea id="info-display"></textarea>
        `;

        const extractButton = document.querySelector('#extract-button');
        const infoDisplay = document.querySelector('#info-display');

        extractButton.addEventListener('click', () => {
            if (extractionInProgress) {
                infoDisplay.value = "正在后台查找，请稍候...";
            } else {
                infoDisplay.value = extractedInfo;
            }
        });

        startProactiveExtraction();
    }

    // --- 后台预提取逻辑（详情页专用） ---
    function startProactiveExtraction() {
        const maxAttempts = 50; // 10秒超时
        const intervalTime = 200;
        let attempts = 0;

        const intervalId = setInterval(() => {
            attempts++;
            if (attempts >= maxAttempts) {
                clearInterval(intervalId);
                extractedInfo = "错误：查找超时。\n无法定位目标元素。\n请确认页面已完全加载或刷新重试。";
                extractionInProgress = false;
                return;
            }

            const microAppHost = document.querySelector('micro-app[name="aftersale"]');
            const shadowRoot = microAppHost ? microAppHost.shadowRoot : null;
            if (!shadowRoot) return;

            const firstStepItem = shadowRoot.querySelector('.step .step-item:first-child');
            if (!firstStepItem) return;

            clearInterval(intervalId);

            const getText = (selector) => {
                const element = firstStepItem.querySelector(selector);
                return element ? element.innerText.trim() : '未找到';
            };

            const findDetailByLabel = (label) => {
                const detailItems = firstStepItem.querySelectorAll('.block-item');
                for (const item of detailItems) {
                    const titleElement = item.querySelector('.block-title');
                    if (titleElement && titleElement.innerText.trim() === label) {
                        const contentElement = item.querySelector('.block-content');
                        const value = contentElement ? contentElement.innerText.trim() : '未找到内容';
                        return `${label} ${value}`;
                    }
                }
                return `${label} 未找到标签`;
            };

            const status = getText('.body .title');
            const processingInfo = getText('.body .tips');
            const refundAmount = findDetailByLabel('退款金额');
            const refundDestination = findDetailByLabel('钱款去向');

            extractedInfo = [
                status,
                processingInfo,
                refundAmount,
                refundDestination
            ].join('\n');

            extractionInProgress = false;
        }, intervalTime);
    }

    // --- 启动脚本 ---
    initializePanel();

})();
